use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use tauri::tray::TrayIconBuilder;

mod http_listener;

pub fn run() {
    // Create shared state for enable/disable functionality
    let is_enabled = Arc::new(AtomicBool::new(true)); // Start enabled by default

    // Clone the state for the HTTP listener thread
    let listener_state = Arc::clone(&is_enabled);
    std::thread::spawn(move || {
        loop {
            if let Err(e) = http_listener::listen(listener_state.clone()) {
                eprintln!("Error in HTTP listener: {}", e);
            }
            std::thread::sleep(std::time::Duration::from_secs(1));
        }
    });
    tauri::Builder::default()
        .setup(move |app| {
            // Clone the state for the tray setup
            let tray_state = Arc::clone(&is_enabled);
            setup_system_tray(app, tray_state)?;
            Ok(())
        })
        .plugin(tauri_plugin_autostart::Builder::new().build())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

fn setup_system_tray(app: &tauri::App, is_enabled: Arc<AtomicBool>) -> tauri::Result<()> {
    // This function will be implemented in the next task
    TrayIconBuilder::new().build(app)?;
    Ok(())
}

use tauri::tray::TrayIconBuilder;

mod http_listener;

pub fn run() {
    std::thread::spawn(move || {
        loop {
            if let Err(e) = http_listener::listen() {
                eprintln!("Error in HTTP listener: {}", e);
            }
            std::thread::sleep(std::time::Duration::from_secs(1));
        }
    });
    tauri::Builder::default()
        .setup(|app| {
            TrayIconBuilder::new().build(app)?;
            Ok(())
        })
        .plugin(tauri_plugin_autostart::Builder::new().build())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
